import { Injectable, Logger } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { AppException } from '@common/exceptions';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { ProviderFineTuneEnum } from '@modules/models/constants/provider.enum';
import { CreateFineTuningJobDto, CreateFineTuningJobResponseDto } from '../dto/user-data-fine-tune/create-fine-tuning-job.dto';
import { UserDataFineTune } from '@modules/models/entities/user-data-fine-tune.entity';
import { UserModelFineTune } from '@modules/models/entities/user-model-fine-tune.entity';
import { FineTuneHistories } from '@modules/models/entities/fine-tune-histories.entity';
import { SystemModels } from '@modules/models/entities/system-models.entity';
import { UserModels } from '@modules/models/entities/user-models.entity';
import { ModelRegistry } from '@modules/models/entities/model-registry.entity';
import { UserRepository } from '@modules/user/repositories/user.repository';
import { UserKeyLlmRepository } from '@modules/models/repositories/user-key-llm.repository';
import { OpenAiService } from '@shared/services/ai/openai.service';
import { GoogleAIService } from '@shared/services/ai/google_ai.service';
import { S3Service } from '@shared/services/s3.service';
import { ApiKeyEncryptionHelper } from '@modules/models/helpers/api-key-encryption.helper';

/**
 * Service xử lý logic tạo fine-tuning job
 */
@Injectable()
export class FineTuningJobService {
  private readonly logger = new Logger(FineTuningJobService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly userKeyLlmRepository: UserKeyLlmRepository,
    private readonly openAiService: OpenAiService,
    private readonly googleAIService: GoogleAIService,
    private readonly s3Service: S3Service,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
  ) {}

  /**
   * Tạo fine-tuning job
   * @param userId ID của người dùng
   * @param dto Thông tin tạo job
   * @param systemApiKey API key hệ thống (fallback nếu user không có key riêng)
   * @returns Thông tin job đã tạo
   */
  async createFineTuningJob(
    userId: number,
    dto: CreateFineTuningJobDto,
    systemApiKey?: string,
  ): Promise<CreateFineTuningJobResponseDto> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1. Validate dataset
      const dataset = await this.validateDataset(dto.datasetId, userId);

      // 2. Validate và lấy thông tin model cơ sở
      const baseModelInfo = await this.validateBaseModel(dto.baseModelId, dto.modelType);

      // 3. Validate provider khớp với model
      if (baseModelInfo.provider !== dto.provider) {
        throw new AppException(
          MODELS_ERROR_CODES.INVALID_PROVIDER,
          `Provider ${dto.provider} không khớp với model ${baseModelInfo.provider}`,
        );
      }

      // 4. Validate và lấy API key (user key hoặc system key)
      const { apiKey, userKeyInfo } = await this.validateAndGetApiKey(
        userId,
        dto.userKeyLlmId,
        dto.provider,
        systemApiKey,
      );

      // 5. Tính toán token và chi phí
      const tokenInfo = await this.calculateTokensAndCost(dataset.trainDataset, baseModelInfo.trainingPricing);

      // 6. Kiểm tra và trừ points của user
      await this.deductUserPoints(userId, tokenInfo.totalCost, queryRunner);

      // 7. Tạo job theo provider
      let jobResult: any;
      let trainingFileId: string | undefined;
      let validationFileId: string | undefined;
      let trainingDataUri: string | undefined;

      if (dto.provider === ProviderFineTuneEnum.OPENAI) {
        // Upload file training lên OpenAI
        trainingFileId = await this.openAiService.uploadTrainingFile(dataset.trainDataset, apiKey);

        // Upload file validation lên OpenAI nếu có
        if (dataset.validDataset) {
          validationFileId = await this.openAiService.uploadTrainingFile(dataset.validDataset, apiKey);
        }

        // Tạo fine-tuning job
        jobResult = await this.openAiService.createFineTuningJob({
          trainingFileId,
          validationFileId,
          model: baseModelInfo.modelId,
          suffix: dto.suffix,
          hyperparameters: dto.hyperparameters ? {
            nEpochs: dto.hyperparameters.epochs,
            batchSize: dto.hyperparameters.batchSize,
            learningRateMultiplier: dto.hyperparameters.learningRate,
          } : undefined,
        }, apiKey);
      } else if (dto.provider === ProviderFineTuneEnum.GOOGLE) {
        // Validate Google Cloud config
        if (!dto.googleCloud) {
          throw new AppException(
            MODELS_ERROR_CODES.MISSING_GOOGLE_CLOUD_CONFIG,
            'Thiếu thông tin Google Cloud configuration',
          );
        }

        // Upload file lên Google Cloud Storage
        trainingDataUri = await this.googleAIService.uploadTrainingFile(
          dataset.trainDataset,
          dto.googleCloud.bucketName,
          apiKey,
        );

        // Tạo fine-tuning job
        jobResult = await this.googleAIService.createFineTuningJob({
          baseModelId: baseModelInfo.modelId,
          displayName: dto.name,
          description: dto.description,
          trainingDataUri,
          hyperParameters: dto.hyperparameters ? {
            epochCount: dto.hyperparameters.epochs,
            batchSize: dto.hyperparameters.batchSize as number,
            learningRate: dto.hyperparameters.learningRate as number,
          } : undefined,
        }, apiKey, dto.googleCloud.projectId, dto.googleCloud.location);
      }

      // 8. Lưu thông tin fine-tuning job vào database
      await this.saveFineTuningJob(
        userId,
        dto,
        dataset,
        baseModelInfo,
        jobResult,
        tokenInfo,
        trainingFileId,
        trainingDataUri,
        userKeyInfo,
        queryRunner,
      );

      await queryRunner.commitTransaction();

      // 9. Lấy số dư còn lại của user
      const user = await this.dataSource.getRepository('User').findOne({ where: { id: userId } });

      this.logger.log(`Fine-tuning job created successfully: ${jobResult.id || jobResult.name}`);

      return {
        jobId: jobResult.id || jobResult.name,
        jobName: jobResult.name,
        status: jobResult.status || jobResult.state,
        baseModel: baseModelInfo.modelId,
        trainingFileId,
        trainingDataUri,
        estimatedTokens: tokenInfo.totalTokens,
        costDeducted: tokenInfo.totalCost,
        remainingBalance: user?.pointsBalance || 0,
        userKeyLlmId: userKeyInfo?.id,
        userKeyLlmName: userKeyInfo?.name,
        createdAt: Date.now(),
      };
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.logger.error(`Error creating fine-tuning job: ${error.message}`, error.stack);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Validate và lấy API key (user key hoặc system key)
   */
  private async validateAndGetApiKey(
    userId: number,
    userKeyLlmId: string | undefined,
    provider: ProviderFineTuneEnum,
    systemApiKey: string | undefined,
  ): Promise<{ apiKey: string; userKeyInfo?: any }> {
    // Nếu user cung cấp key riêng
    if (userKeyLlmId) {
      const userKey = await this.userKeyLlmRepository.findOne({
        where: {
          id: userKeyLlmId,
          userId,
          provider: provider as any, // Cast to match ProviderEnum
          deletedAt: null as any
        },
      });

      if (!userKey) {
        throw new AppException(
          MODELS_ERROR_CODES.USER_KEY_LLM_NOT_FOUND,
          'Không tìm thấy user key LLM hoặc key không thuộc về bạn',
        );
      }

      // Giải mã API key
      const decryptedApiKey = this.apiKeyEncryptionHelper.decryptUserApiKey(
        userKey.apiKey,
        userId,
      );

      return {
        apiKey: decryptedApiKey,
        userKeyInfo: {
          id: userKey.id,
          name: userKey.name,
          provider: userKey.provider,
        },
      };
    }

    // Nếu không có user key, sử dụng system key
    if (!systemApiKey) {
      throw new AppException(
        MODELS_ERROR_CODES.MISSING_API_KEY,
        'Không có API key để thực hiện fine-tuning. Vui lòng cung cấp user key hoặc system key.',
      );
    }

    return { apiKey: systemApiKey };
  }

  /**
   * Validate dataset
   */
  private async validateDataset(datasetId: string, userId: number): Promise<UserDataFineTune> {
    const dataset = await this.dataSource.getRepository(UserDataFineTune).findOne({
      where: { id: datasetId, userId, deletedAt: null as any },
    });

    if (!dataset) {
      throw new AppException(
        MODELS_ERROR_CODES.DATASET_NOT_FOUND,
        'Không tìm thấy dataset hoặc bạn không có quyền truy cập',
      );
    }

    if (!dataset.trainDataset) {
      throw new AppException(
        MODELS_ERROR_CODES.MISSING_TRAINING_DATA,
        'Dataset không có dữ liệu training',
      );
    }

    return dataset;
  }

  /**
   * Validate và lấy thông tin model cơ sở
   */
  private async validateBaseModel(modelId: string, modelType: 'system' | 'user'): Promise<any> {
    let model: any;
    let modelRegistry: ModelRegistry | null = null;

    if (modelType === 'system') {
      // Lấy từ system_models
      const systemModel = await this.dataSource.getRepository(SystemModels).findOne({
        where: { id: modelId, active: true },
      });

      if (!systemModel) {
        throw new AppException(
          MODELS_ERROR_CODES.MODEL_NOT_FOUND,
          'Không tìm thấy system model hoặc model không active',
        );
      }

      // Lấy thông tin từ model registry thông qua modelRegistryId
      modelRegistry = await this.dataSource.getRepository(ModelRegistry).findOne({
        where: {
          id: systemModel.modelRegistryId,
          deletedAt: null as any,
        },
      });

      model = { ...systemModel, modelId: systemModel.modelId };
    } else {
      // Lấy từ user_models
      const userModel = await this.dataSource.getRepository(UserModels).findOne({
        where: { id: modelId },
      });

      if (!userModel) {
        throw new AppException(
          MODELS_ERROR_CODES.MODEL_NOT_FOUND,
          'Không tìm thấy user model',
        );
      }

      // Lấy thông tin từ model registry thông qua modelRegistryId
      modelRegistry = await this.dataSource.getRepository(ModelRegistry).findOne({
        where: {
          id: userModel.modelRegistryId,
          deletedAt: null as any,
        },
      });

      model = { ...userModel, modelId: userModel.modelId };
    }

    if (!modelRegistry) {
      throw new AppException(
        MODELS_ERROR_CODES.MODEL_REGISTRY_NOT_FOUND,
        'Không tìm thấy thông tin model registry',
      );
    }

    return {
      ...model,
      trainingPricing: modelRegistry.trainingPricing,
      provider: modelRegistry.provider,
    };
  }

  /**
   * Tính toán số lượng token và chi phí
   */
  private async calculateTokensAndCost(trainFileKey: string, trainingPricing: number): Promise<{
    totalTokens: number;
    totalCost: number;
  }> {
    try {
      // Tải file từ S3 và đếm token
      const fileBytes = await this.s3Service.downloadFileAsBytes(trainFileKey);
      const fileContent = new TextDecoder().decode(fileBytes);
      const lines = fileContent.split('\n').filter(line => line.trim());
      
      let totalTokens = 0;
      
      // Đếm token cho mỗi dòng JSONL
      for (const line of lines) {
        try {
          const data = JSON.parse(line);
          // Ước tính token (1 token ≈ 4 ký tự cho tiếng Anh, 1-2 ký tự cho tiếng Việt)
          const content = JSON.stringify(data);
          const estimatedTokens = Math.ceil(content.length / 3); // Ước tính conservative
          totalTokens += estimatedTokens;
        } catch (parseError) {
          this.logger.warn(`Không thể parse dòng JSONL: ${line}`);
        }
      }

      // Tính chi phí (trainingPricing là cost per 1K tokens)
      const totalCost = Math.ceil((totalTokens / 1000) * trainingPricing);

      return { totalTokens, totalCost };
    } catch (error) {
      this.logger.error(`Error calculating tokens: ${error.message}`, error.stack);
      throw new AppException(
        MODELS_ERROR_CODES.TOKEN_CALCULATION_FAILED,
        'Không thể tính toán số lượng token',
      );
    }
  }

  /**
   * Trừ points của user
   */
  private async deductUserPoints(userId: number, cost: number, queryRunner: any): Promise<void> {
    try {
      await this.userRepository.updateUserBalance(userId, -cost);
      this.logger.log(`Deducted ${cost} R-Points from user ${userId}`);
    } catch (error) {
      throw new AppException(
        MODELS_ERROR_CODES.INSUFFICIENT_POINTS,
        error.message || 'Số dư R-Points không đủ',
      );
    }
  }

  /**
   * Lưu thông tin fine-tuning job vào database
   */
  private async saveFineTuningJob(
    userId: number,
    dto: CreateFineTuningJobDto,
    dataset: UserDataFineTune,
    baseModelInfo: any,
    jobResult: any,
    tokenInfo: any,
    trainingFileId: string | undefined,
    trainingDataUri: string | undefined,
    userKeyInfo: any,
    queryRunner: any,
  ): Promise<UserModelFineTune> {
    // Tạo bản ghi fine-tune history trước
    const fineTuneHistory = queryRunner.manager.create(FineTuneHistories, {
      modelName: dto.name,
      token: tokenInfo.totalTokens,
      method: {
        provider: dto.provider,
        hyperparameters: dto.hyperparameters,
        trainingFile: trainingFileId || trainingDataUri,
      },
      metadata: {
        jobId: jobResult.id || jobResult.name,
        jobName: jobResult.name,
        baseModel: baseModelInfo.modelId,
        datasetId: dto.datasetId,
        costDeducted: tokenInfo.totalCost,
        userKeyLlmId: userKeyInfo?.id,
        description: dto.description,
      },
      userId,
      startDate: Date.now(),
      endDate: Date.now(), // Sẽ được cập nhật khi job hoàn thành
    });

    const savedHistory = await queryRunner.manager.save(fineTuneHistory);

    // Tạo bản ghi user model fine-tune
    const fineTuneModel = queryRunner.manager.create(UserModelFineTune, {
      modelId: jobResult.fineTunedModel || `${dto.name}-${Date.now()}`,
      modelBase: baseModelInfo.modelId,
      modelRegistryId: baseModelInfo.registryId || baseModelInfo.id,
      llmKeyId: userKeyInfo?.id || null,
      detailId: savedHistory.id,
    });

    return await queryRunner.manager.save(fineTuneModel);
  }
}
