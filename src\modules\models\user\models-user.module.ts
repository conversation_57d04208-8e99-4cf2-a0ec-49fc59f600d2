import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { ServicesModule } from '@shared/services/services.module';

// Import entities
import * as entities from '../entities';

// Import repositories
import * as repositories from '../repositories';
import { UserModelsRepository } from '../repositories/user-models.repository';
import { SystemModelKeyLlmRepository } from '../repositories/system-model-key-llm.repository';
import { UserModelKeyLlmRepository } from '../repositories/user-model-key-llm.repository';

// Import controllers
import {
  UserDataFineTuneController,
  UserKeyLlmController,
  UserModelsController,
} from './controllers';

// Import services
import {
  UserDataFineTuneService,
  UserKeyLlmService,
  UserModelsService,
} from './services';

// Import shared services
import { UserModelSyncService } from '../services/user-model-sync.service';
import { ModelDiscoveryService } from '../services/model-discovery.service';
import { PatternMatchingEngineService } from '../services/pattern-matching-engine.service';
import { BulkModelOperationsService } from '../services/bulk-model-operations.service';

// Import helpers
import { ApiKeyEncryptionHelper } from '../helpers/api-key-encryption.helper';
import { FineTuningJobController } from './controllers/fine-tuning-job.controller';

/**
 * Module quản lý models cho user
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      entities.ModelRegistry,
      entities.SystemKeyLlm,
      entities.FineTuneHistories,
      entities.UserKeyLlm,
      entities.UserDataFineTune,
      entities.AdminDataFineTune,
      // New entities
      entities.SystemModels,
      entities.UserModels,
      entities.UserModelKeyLlm,
      entities.UserModelFineTune,
      entities.SystemModelKeyLlm,
    ]),
    ConfigModule,
    ServicesModule,
  ],
  controllers: [
    UserDataFineTuneController,
    UserKeyLlmController,
    UserModelsController,
    FineTuningJobController
  ],
  providers: [
    // Services
    UserDataFineTuneService,
    UserKeyLlmService,
    UserModelsService,
    UserModelSyncService,
    ModelDiscoveryService,
    PatternMatchingEngineService,
    BulkModelOperationsService,

    // Repositories
    repositories.ModelRegistryRepository,
    repositories.SystemKeyLlmRepository,
    repositories.FineTuneHistoriesRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserDataFineTuneRepository,
    repositories.AdminDataFineTuneRepository,
    repositories.SystemModelsRepository,
    repositories.UserKeyLlmRepository,
    repositories.UserModelFineTuneRepository,
    UserModelsRepository,
    SystemModelKeyLlmRepository,
    UserModelKeyLlmRepository,

    // Helpers
    ApiKeyEncryptionHelper,
  ],
  exports: [
    UserDataFineTuneService,
    UserKeyLlmService,
    UserModelsService,
  ],
})
export class ModelsUserModule {}
