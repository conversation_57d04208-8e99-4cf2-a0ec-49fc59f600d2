import { Controller, Post, Body, UseGuards, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { FineTuningJobService } from '../services/fine-tuning-job.service';
import { CreateFineTuningJobDto, CreateFineTuningJobResponseDto } from '../dto/user-data-fine-tune/create-fine-tuning-job.dto';
import { MODELS_ERROR_CODES } from '@modules/models/exceptions/models.exception';
import { JwtUserGuard } from '@/modules/auth/guards';
import { ApiResponseDto } from '@/common/response';
import { CurrentUser } from '@/modules/auth/decorators';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các API liên quan đến fine-tuning jobs
 */
@ApiTags(SWAGGER_API_TAGS.USER_FINETUNING_JOB)
@Controller('user/fine-tuning-jobs')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class FineTuningJobController {
  constructor(private readonly fineTuningJobService: FineTuningJobService) {}

  /**
   * Tạo fine-tuning job mới
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo fine-tuning job mới',
    description: `
    Tạo fine-tuning job mới cho OpenAI hoặc Google AI.
    
    **Quy trình:**
    1. Validate dataset và model cơ sở
    2. Tính toán token và chi phí
    3. Trừ R-Points từ tài khoản user
    4. Upload file training lên provider
    5. Tạo fine-tuning job
    6. Lưu thông tin vào database
    
    **Lưu ý:**
    - Nếu cung cấp userKeyLlmId, sẽ sử dụng API key riêng của user
    - Nếu không có userKeyLlmId, sẽ sử dụng system API key
    - Chi phí được tính dựa trên số token và trainingPricing của model
    - User phải có đủ R-Points để thực hiện fine-tuning
    `,
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Fine-tuning job đã được tạo thành công',
    type: ApiResponseDto<CreateFineTuningJobResponseDto>,
  })
  @ApiErrorResponse(
    MODELS_ERROR_CODES.INVALID_INPUT,
    MODELS_ERROR_CODES.DATASET_NOT_FOUND,
    MODELS_ERROR_CODES.MODEL_NOT_FOUND,
    MODELS_ERROR_CODES.INVALID_PROVIDER,
    MODELS_ERROR_CODES.MISSING_API_KEY,
    MODELS_ERROR_CODES.MISSING_GOOGLE_CLOUD_CONFIG,
    MODELS_ERROR_CODES.TOKEN_CALCULATION_FAILED,
    MODELS_ERROR_CODES.INSUFFICIENT_POINTS,
    MODELS_ERROR_CODES.FINE_TUNING_JOB_CREATION_FAILED,
  )
  async createFineTuningJob(
    @CurrentUser() user: { id: number },
    @Body() createDto: CreateFineTuningJobDto,
  ): Promise<ApiResponseDto<CreateFineTuningJobResponseDto>> {
    const result = await this.fineTuningJobService.createFineTuningJob(
      user.id,
      createDto,
    );

    return ApiResponseDto.success(
      result,
      'Fine-tuning job đã được tạo thành công',
    );
  }
}
